# Headless Browser Configuration

Your rent house notifier now supports **invisible headless browser mode** with advanced Cloudflare bypass capabilities! 🎉

## What Changed

### ✅ Before (Visible Browser)
- Browser window would open and be visible on your screen
- Slower performance due to UI rendering
- Could be distracting during operation
- Required display/desktop environment

### 🚀 After (Headless Browser)
- <PERSON><PERSON><PERSON> runs completely invisibly in the background
- Faster performance (no UI rendering)
- No interruptions to your workflow
- Works on servers without display
- **Still bypasses Cloudflare challenges!**

## Configuration

### Environment Variables

Add these to your `.env` file:

```bash
# Use browser-based monitoring (true/false)
USE_BROWSER=true

# Run browser in headless mode (true/false) - NEW!
HEADLESS_BROWSER=true
```

### Options

| Setting | Description |
|---------|-------------|
| `HEADLESS_BROWSER=true` | **Invisible browser** (recommended) |
| `HEADLESS_BROWSER=false` | **Visible browser** (for debugging) |

## Features

### 🔒 Enhanced Cloudflare Bypass
- Advanced challenge detection
- Improved stealth techniques
- Better success rate with headless mode
- Automatic retry logic

### 🎭 Advanced Stealth Features
- **Updated User Agents**: Latest Chrome/Firefox versions
- **Random Viewports**: Mimics different screen sizes
- **Enhanced Headers**: More realistic browser headers
- **WebGL Spoofing**: Hides automation signatures
- **Hardware Simulation**: Fake device specs

### ⚡ Performance Improvements
- Faster page loading
- Reduced memory usage
- Optimized for server environments
- Better resource management

## Testing

### Test Headless Mode
```bash
node test-headless.js
```

### Test Visible Mode
```bash
node test-headless.js --visible
```

### Test Both Modes
```bash
node test-headless.js --both
```

## Migration Guide

### Existing Users
1. Update your `.env` file:
   ```bash
   # Add this line
   HEADLESS_BROWSER=true
   ```

2. Your existing `USE_BROWSER=true` setting will now use headless mode by default

3. Test the new configuration:
   ```bash
   node test-headless.js
   ```

### New Users
1. Copy `.env.example` to `.env`
2. Configure your Slack webhook
3. Set `USE_BROWSER=true` and `HEADLESS_BROWSER=true`
4. Run the application

## Troubleshooting

### If Cloudflare Challenges Fail
1. Try visible mode for debugging:
   ```bash
   HEADLESS_BROWSER=false
   ```

2. Check the debug output:
   ```bash
   DEBUG_HTML=true node index.js
   ```

3. Test with the test script:
   ```bash
   node test-headless.js --both
   ```

### Performance Issues
- Headless mode should be faster
- If you experience issues, try visible mode temporarily
- Check system resources (RAM/CPU)

### Proxy Issues
- Proxies work the same in both modes
- Test proxies with: `node test-proxy.js`

## Technical Details

### Browser Arguments
The headless mode uses optimized Chrome arguments:
- Disabled images for faster loading
- Reduced memory usage
- Enhanced stealth features
- Better Cloudflare bypass

### Stealth Techniques
- Navigator property spoofing
- Plugin simulation
- Hardware fingerprint masking
- WebGL vendor spoofing
- Language preference simulation

## Benefits

### 🎯 For Regular Use
- **Invisible operation**: No browser windows
- **Faster monitoring**: Reduced overhead
- **Server friendly**: Works without display
- **Less distracting**: Runs in background

### 🛡️ For Cloudflare Sites
- **Better success rate**: Enhanced bypass
- **Automatic handling**: No manual intervention
- **Robust detection**: Multiple challenge indicators
- **Retry logic**: Handles timeouts gracefully

### 💻 For Developers
- **Easy testing**: Test scripts included
- **Flexible configuration**: Switch modes easily
- **Debug support**: Visible mode for troubleshooting
- **Better logging**: Enhanced status messages

## Compatibility

- ✅ Works on all platforms (Windows, Mac, Linux)
- ✅ Compatible with existing configurations
- ✅ Supports all existing proxy settings
- ✅ Maintains all scraping functionality
- ✅ Works with or without display environment

---

**Enjoy your new invisible, faster, and more reliable rent house monitoring! 🏠✨**
