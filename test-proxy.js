#!/usr/bin/env node

/**
 * Proxy Testing Script for Rent House Notify Project
 * This script tests all proxies in the browser-monitor.js file
 */

require('dotenv').config();
const BrowserMonitor = require('./src/browser-monitor');

async function testProxies() {
    console.log('🧪 Starting Proxy Testing Script...');
    console.log('=' .repeat(60));
    
    // Create a browser monitor instance (we don't need Slack for testing)
    const monitor = new BrowserMonitor('dummy-webhook-url');
    
    try {
        // Test all proxies
        const results = await monitor.testAllProxies();
        
        console.log('\n' + '='.repeat(60));
        console.log('📊 FINAL PROXY TEST RESULTS');
        console.log('='.repeat(60));
        
        console.log(`Total proxies tested: ${results.total}`);
        console.log(`Working proxies: ${results.working.length}`);
        console.log(`Failed proxies: ${results.failed.length}`);
        console.log(`Success rate: ${((results.working.length / results.total) * 100).toFixed(1)}%`);
        
        if (results.working.length > 0) {
            console.log('\n✅ WORKING PROXIES:');
            console.log('-'.repeat(40));
            results.working.forEach((result, index) => {
                console.log(`${index + 1}. ${result.proxy}`);
                console.log(`   IP: ${result.ip}`);
                console.log('');
            });
            
            // Save working proxies to a file
            const fs = require('fs');
            const workingProxies = results.working.map(r => r.proxy);
            fs.writeFileSync('./working-proxies.json', JSON.stringify({
                tested_at: new Date().toISOString(),
                total_tested: results.total,
                working_count: results.working.length,
                success_rate: ((results.working.length / results.total) * 100).toFixed(1) + '%',
                working_proxies: workingProxies,
                detailed_results: results.working
            }, null, 2));
            
            console.log('💾 Working proxies saved to working-proxies.json');
        } else {
            console.log('\n❌ NO WORKING PROXIES FOUND');
            console.log('All proxies failed. You may need to:');
            console.log('1. Update the proxy list with fresh proxies');
            console.log('2. Check your internet connection');
            console.log('3. Try different proxy sources');
        }
        
        if (results.failed.length > 0) {
            console.log('\n❌ FAILED PROXIES:');
            console.log('-'.repeat(40));
            results.failed.slice(0, 10).forEach((result, index) => {
                console.log(`${index + 1}. ${result.proxy} - ${result.error}`);
            });
            
            if (results.failed.length > 10) {
                console.log(`... and ${results.failed.length - 10} more failed proxies`);
            }
        }
        
        console.log('\n🔍 RECOMMENDATIONS:');
        console.log('-'.repeat(40));
        
        if (results.working.length === 0) {
            console.log('• All proxies failed - consider updating the proxy list');
            console.log('• Free proxies often have short lifespans');
            console.log('• Consider using paid proxy services for better reliability');
        } else if (results.working.length < results.total * 0.3) {
            console.log('• Low success rate - consider refreshing the proxy list');
            console.log('• Remove non-working proxies to improve performance');
        } else {
            console.log('• Good proxy success rate!');
            console.log('• Consider removing failed proxies from the list');
        }
        
        console.log('\n✅ Proxy testing completed!');
        
    } catch (error) {
        console.error('❌ Error during proxy testing:', error.message);
        console.error(error.stack);
    }
}

// Test a single proxy
async function testSingleProxy(proxyString) {
    console.log(`🔍 Testing single proxy: ${proxyString}`);
    
    const monitor = new BrowserMonitor('dummy-webhook-url');
    
    try {
        const result = await monitor.testProxy(proxyString);
        
        if (result.working) {
            console.log(`✅ Proxy is working!`);
            console.log(`   Proxy: ${result.proxy}`);
            console.log(`   IP: ${result.ip}`);
        } else {
            console.log(`❌ Proxy failed: ${result.error}`);
        }
        
        return result;
    } catch (error) {
        console.error('❌ Error testing proxy:', error.message);
        return { working: false, error: error.message, proxy: proxyString };
    }
}

// Main execution
async function main() {
    const args = process.argv.slice(2);
    
    if (args.length > 0) {
        // Test a single proxy if provided as argument
        const proxyToTest = args[0];
        console.log(`🎯 Testing single proxy: ${proxyToTest}`);
        await testSingleProxy(proxyToTest);
    } else {
        // Test all proxies
        await testProxies();
    }
}

// Handle graceful shutdown
process.on('SIGINT', () => {
    console.log('\n🛑 Proxy testing interrupted');
    process.exit(0);
});

// Start the testing
if (require.main === module) {
    main().catch(error => {
        console.error('❌ Fatal error:', error);
        process.exit(1);
    });
}

module.exports = { testProxies, testSingleProxy };
