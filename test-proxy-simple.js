#!/usr/bin/env node

/**
 * Simple HTTP Proxy Testing Script
 * Tests proxies using HTTP requests instead of browser automation
 * Much faster than the browser-based test
 */

const axios = require('axios');
const { HttpsProxyAgent } = require('https-proxy-agent');

// Working proxy list from browser-monitor.js (tested and verified)
// Last tested: 2025-06-13 - Success rate: 75.0% (6/8 working)
const freeProxies = [
    // Working proxies verified on 2025-06-13 (re-tested)
    "***********:17981",      // IP: ***********
    "*************:1080",     // IP: *************
    "************:8080",      // IP: ************
    "*********:8080",         // IP: *********
    "************:8880",      // IP: **************
    "***********:80",         // IP: ***********

    // Add new proxies to test here
    // This script will test all proxies in this array
];

async function testProxyHTTP(proxy, timeout = 10000) {
    console.log(`🔍 Testing proxy: ${proxy}`);

    try {
        // Create proxy agent
        const proxyUrl = `http://${proxy}`;
        const agent = new HttpsProxyAgent(proxyUrl);

        // Test with IP checking service
        const response = await axios.get('https://httpbin.org/ip', {
            httpsAgent: agent,
            timeout: timeout,
            headers: {
                'User-Agent': 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36'
            }
        });

        if (response.data && response.data.origin) {
            const detectedIP = response.data.origin;
            console.log(`✅ Proxy ${proxy} working - IP: ${detectedIP}`);
            return {
                working: true,
                ip: detectedIP,
                proxy: proxy,
                responseTime: response.headers['x-response-time'] || 'unknown'
            };
        } else {
            console.log(`❌ Proxy ${proxy} failed - Invalid response`);
            return { working: false, error: 'Invalid response', proxy: proxy };
        }

    } catch (error) {
        const errorMsg = error.code || error.message || 'Unknown error';
        console.log(`❌ Proxy ${proxy} failed - ${errorMsg}`);
        return { working: false, error: errorMsg, proxy: proxy };
    }
}

async function testAllProxiesHTTP() {
    console.log('🚀 Starting HTTP-based proxy testing...');
    console.log(`📊 Testing ${freeProxies.length} proxies with HTTP requests`);
    console.log('='.repeat(60));

    const results = {
        total: freeProxies.length,
        working: [],
        failed: [],
        startTime: Date.now()
    };

    // Test proxies in parallel (but limit concurrency)
    const concurrency = 5; // Test 5 proxies at a time
    const chunks = [];

    for (let i = 0; i < freeProxies.length; i += concurrency) {
        chunks.push(freeProxies.slice(i, i + concurrency));
    }

    for (let chunkIndex = 0; chunkIndex < chunks.length; chunkIndex++) {
        const chunk = chunks[chunkIndex];
        console.log(`\n📦 Testing chunk ${chunkIndex + 1}/${chunks.length} (${chunk.length} proxies)`);

        const promises = chunk.map(proxy => testProxyHTTP(proxy));
        const chunkResults = await Promise.all(promises);

        chunkResults.forEach(result => {
            if (result.working) {
                results.working.push(result);
            } else {
                results.failed.push(result);
            }
        });

        // Progress update
        const tested = (chunkIndex + 1) * concurrency;
        const progress = Math.min(tested, freeProxies.length);
        console.log(`📈 Progress: ${progress}/${freeProxies.length} (${((progress / freeProxies.length) * 100).toFixed(1)}%)`);
    }

    const totalTime = ((Date.now() - results.startTime) / 1000).toFixed(1);

    console.log('\n' + '='.repeat(60));
    console.log('📊 HTTP PROXY TEST RESULTS');
    console.log('='.repeat(60));
    console.log(`Total time: ${totalTime} seconds`);
    console.log(`Total proxies tested: ${results.total}`);
    console.log(`Working proxies: ${results.working.length}`);
    console.log(`Failed proxies: ${results.failed.length}`);
    console.log(`Success rate: ${((results.working.length / results.total) * 100).toFixed(1)}%`);

    if (results.working.length > 0) {
        console.log('\n✅ WORKING PROXIES:');
        console.log('-'.repeat(40));
        results.working.forEach((result, index) => {
            console.log(`${index + 1}. ${result.proxy} (IP: ${result.ip})`);
        });

        // Save results
        const fs = require('fs');
        fs.writeFileSync('./working-proxies-http.json', JSON.stringify({
            tested_at: new Date().toISOString(),
            test_duration_seconds: totalTime,
            total_tested: results.total,
            working_count: results.working.length,
            success_rate: ((results.working.length / results.total) * 100).toFixed(1) + '%',
            working_proxies: results.working.map(r => r.proxy),
            detailed_results: results.working
        }, null, 2));

        console.log('\n💾 Results saved to working-proxies-http.json');
    } else {
        console.log('\n❌ NO WORKING PROXIES FOUND');
    }

    return results;
}

async function main() {
    const args = process.argv.slice(2);

    if (args.length > 0) {
        // Test single proxy
        const proxy = args[0];
        await testProxyHTTP(proxy);
    } else {
        // Test all proxies
        await testAllProxiesHTTP();
    }
}

// Handle graceful shutdown
process.on('SIGINT', () => {
    console.log('\n🛑 HTTP proxy testing interrupted');
    process.exit(0);
});

if (require.main === module) {
    main().catch(error => {
        console.error('❌ Fatal error:', error);
        process.exit(1);
    });
}

module.exports = { testProxyHTTP, testAllProxiesHTTP };
