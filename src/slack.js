const { IncomingWebhook } = require("@slack/webhook");

class SlackNotifier {
  constructor(webhookUrl) {
    if (!webhookUrl) {
      throw new Error("Slack webhook URL is required");
    }
    this.webhook = new IncomingWebhook(webhookUrl);
  }

  async sendNewItemNotification(siteName, newItems) {
    try {
      await this.sendItemsNotification(
        siteName,
        newItems,
        "🔔 New Items Found",
        "new"
      );
    } catch (error) {
      console.error(
        `❌ [${siteName}] Error sending new items notification:`,
        error
      );
    }
  }

  async sendInitialItemsNotification(siteName, items) {
    try {
      await this.sendItemsNotification(
        siteName,
        items,
        "🎉 Initial Items from First Page",
        "initial"
      );
    } catch (error) {
      console.error(
        `❌ [${siteName}] Error sending initial items notification:`,
        error
      );
    }
  }

  async sendItemsNotification(siteName, items, headerText, type) {
    try {
      const blocks = [
        {
          type: "header",
          text: {
            type: "plain_text",
            text: `${headerText} - ${siteName}`,
          },
        },
        {
          type: "section",
          text: {
            type: "mrkdwn",
            text: `*Website:* ${siteName}\n*Found:* ${
              items.length
            } ${type} item${items.length > 1 ? "s" : ""}\n*Source:* ${
              items[0]?.domain || "Unknown"
            }`,
          },
        },
      ];

      // Add items in groups of 5 to avoid Slack block limits
      const itemsPerBlock = 5;
      for (let i = 0; i < items.length; i += itemsPerBlock) {
        const itemGroup = items.slice(i, i + itemsPerBlock);

        for (const item of itemGroup) {
          // Build detailed item description
          let itemDescription = `*<${item.url}|${item.title}>*\n💰 ${item.price}`;

          // Add area and bedroom info if available
          if (item.area || item.bedroom) {
            const details = [];
            if (item.area) details.push(`📐 ${item.area}`);
            if (item.bedroom) details.push(`🛏️ ${item.bedroom}`);
            itemDescription += `\n${details.join(" • ")}`;
          }

          // Add location if available
          if (item.location) {
            itemDescription += `\n📍 ${item.location}`;
          }

          // Add posting time if available
          if (item.postedTime) {
            itemDescription += `\n⏰ ${item.postedTime}`;
          }

          // Add source
          itemDescription += `\n🏢 ${item.source}`;

          const itemBlock = {
            type: "section",
            text: {
              type: "mrkdwn",
              text: itemDescription,
            },
          };

          // Add image if available
          if (item.image && item.image.startsWith("http")) {
            itemBlock.accessory = {
              type: "image",
              image_url: item.image,
              alt_text: item.title || "Property image",
            };
          }

          blocks.push(itemBlock);
        }

        // Add divider between groups (except for the last group)
        if (i + itemsPerBlock < items.length) {
          blocks.push({
            type: "divider",
          });
        }
      }

      // Add timestamp
      blocks.push({
        type: "context",
        elements: [
          {
            type: "mrkdwn",
            text: `📅 ${
              type === "initial" ? "First check" : "Checked"
            } at ${new Date().toLocaleString("vi-VN", {
              timeZone: "Asia/Ho_Chi_Minh",
            })} (Vietnam time)`,
          },
        ],
      });

      const message = {
        text: `${headerText} - ${siteName} (${items.length} items)`,
        blocks: blocks,
      };

      await this.webhook.send(message);
      console.log(
        `✅ [${siteName}] Slack notification sent for ${items.length} ${type} items`
      );
    } catch (error) {
      console.error(
        `❌ [${siteName}] Error sending items notification:`,
        error
      );
    }
  }

  async sendErrorNotification(siteName, error) {
    try {
      // Determine error type and provide helpful context
      let errorType = "🚫 General Error";
      let errorAdvice =
        "Please check the website manually and verify the URL is correct.";

      if (
        error.message.includes("403") ||
        error.message.includes("Access denied")
      ) {
        errorType = "🛡️ Access Blocked";
        errorAdvice =
          "Website is blocking automated requests. Consider enabling browser mode or increasing check intervals.";
      } else if (
        error.message.includes("timeout") ||
        error.message.includes("TIMEOUT")
      ) {
        errorType = "⏰ Timeout Error";
        errorAdvice =
          "Website took too long to respond. This might be temporary.";
      } else if (
        error.message.includes("404") ||
        error.message.includes("Not Found")
      ) {
        errorType = "🔍 Page Not Found";
        errorAdvice =
          "The URL might have changed. Please verify the website URL.";
      } else if (
        error.message.includes("500") ||
        error.message.includes("Server Error")
      ) {
        errorType = "🔧 Server Error";
        errorAdvice =
          "Website server is having issues. This is usually temporary.";
      }

      const message = {
        text: `⚠️ [${siteName}] Monitoring Error - ${errorType}`,
        blocks: [
          {
            type: "header",
            text: {
              type: "plain_text",
              text: `⚠️ ${siteName} - Monitoring Error`,
            },
          },
          {
            type: "section",
            text: {
              type: "mrkdwn",
              text: `*Website:* ${siteName}\n*Error Type:* ${errorType}`,
            },
          },
          {
            type: "section",
            text: {
              type: "mrkdwn",
              text: `*Error Details:* ${error.message}`,
            },
          },
          {
            type: "section",
            text: {
              type: "mrkdwn",
              text: `*Suggestion:* ${errorAdvice}`,
            },
          },
          {
            type: "context",
            elements: [
              {
                type: "mrkdwn",
                text: `📅 Error occurred at ${new Date().toLocaleString(
                  "vi-VN",
                  { timeZone: "Asia/Ho_Chi_Minh" }
                )} (Vietnam time)`,
              },
            ],
          },
        ],
      };

      await this.webhook.send(message);
      console.log(`⚠️ [${siteName}] Error notification sent: ${errorType}`);
    } catch (slackError) {
      console.error(
        `❌ [${siteName}] Error sending Slack error notification:`,
        slackError
      );
    }
  }

  async sendStartupNotification(sites) {
    try {
      const sitesList = sites.map((site) => `• ${site.name}`).join("\n");

      const message = {
        text: "🚀 Website Monitor Started - Monitoring Multiple Sites",
        blocks: [
          {
            type: "header",
            text: {
              type: "plain_text",
              text: "🚀 Website Monitor Started",
            },
          },
          {
            type: "section",
            text: {
              type: "mrkdwn",
              text: `The website monitoring service has started successfully!\n\n*Monitoring ${sites.length} websites:*\n${sitesList}`,
            },
          },
          {
            type: "section",
            text: {
              type: "mrkdwn",
              text: `*Check Interval:* Every ${
                process.env.CHECK_INTERVAL_MINUTES || 240
              } minutes\n*Browser Mode:* ${
                process.env.USE_BROWSER === "true" ? "Enabled" : "Disabled"
              }`,
            },
          },
          {
            type: "context",
            elements: [
              {
                type: "mrkdwn",
                text: `📅 Started at ${new Date().toLocaleString("vi-VN", {
                  timeZone: "Asia/Ho_Chi_Minh",
                })} (Vietnam time)`,
              },
            ],
          },
        ],
      };

      await this.webhook.send(message);
      console.log("🚀 Startup notification sent");
    } catch (error) {
      console.error("❌ Error sending startup notification:", error);
    }
  }

  async sendSuccessfulCheckNotification(siteName, itemCount) {
    try {
      const message = {
        text: `✅ [${siteName}] Check completed - No new items`,
        blocks: [
          {
            type: "section",
            text: {
              type: "mrkdwn",
              text: `✅ *${siteName}* - Check completed successfully\n📊 Found ${itemCount} total items (no new items)\n📅 ${new Date().toLocaleString(
                "vi-VN",
                { timeZone: "Asia/Ho_Chi_Minh" }
              )}`,
            },
          },
        ],
      };

      // Only send this notification in debug mode to avoid spam
      if (process.env.LOG_LEVEL === "debug") {
        await this.webhook.send(message);
        console.log(`✅ [${siteName}] Success notification sent`);
      }
    } catch (error) {
      console.error(
        `❌ [${siteName}] Error sending success notification:`,
        error
      );
    }
  }
}

module.exports = SlackNotifier;
