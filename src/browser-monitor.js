const puppeteer = require("puppeteer-extra");
const StealthPlugin = require("puppeteer-extra-plugin-stealth");
const useProxy = require("puppeteer-page-proxy");
const Storage = require("./storage");
const SlackNotifier = require("./slack");

// Use stealth plugin to avoid detection
puppeteer.use(StealthPlugin());

class BrowserMonitor {
  constructor(slackWebhookUrl) {
    this.storage = new Storage();
    this.slackNotifier = new SlackNotifier(slackWebhookUrl);
    this.browser = null;
    this.currentProxy = null;

    // List of working proxies (tested and verified)
    // Last tested: 2025-06-13 - Success rate: 75.0% (6/8 working)
    // Note: Free proxies change frequently, test regularly with test-proxy-simple.js
    this.freeProxies = [
      // Working proxies verified on 2025-06-13 (re-tested)
      "***********:17981",      // IP: ***********
      "*************:1080",     // IP: *************
      "************:8080",      // IP: ************
      "*********:8080",         // IP: *********
      "************:8880",      // IP: **************
      "***********:80",         // IP: ***********

      // Backup: Add new working proxies here
      // Test new proxies with: node test-proxy-simple.js
      // Or test all proxies with: node test-proxy-simple.js
    ];

    // User agents from different countries/browsers
    this.userAgents = [
      "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/120.0.0.0 Safari/537.36", // US
      "Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/120.0.0.0 Safari/537.36", // US Mac
      "Mozilla/5.0 (X11; Linux x86_64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/120.0.0.0 Safari/537.36", // Linux
      "Mozilla/5.0 (Windows NT 10.0; Win64; x64; rv:109.0) Gecko/20100101 Firefox/121.0", // Firefox Windows
      "Mozilla/5.0 (Macintosh; Intel Mac OS X 10.15; rv:109.0) Gecko/20100101 Firefox/121.0", // Firefox Mac
    ];
  }

  getRandomProxy() {
    return this.freeProxies[
      Math.floor(Math.random() * this.freeProxies.length)
    ];
  }

  getRandomUserAgent() {
    return this.userAgents[Math.floor(Math.random() * this.userAgents.length)];
  }

  async initBrowser() {
    if (!this.browser) {
      // Select random proxy and user agent
      this.currentProxy = this.getRandomProxy();
      const userAgent = this.getRandomUserAgent();

      console.log(`🌐 Starting browser from random location...`);
      console.log(`🌍 Using proxy: ${this.currentProxy}`);
      console.log(`🖥️ Using user agent: ${userAgent.substring(0, 50)}...`);

      this.browser = await puppeteer.launch({
        headless: false, // Show browser window like a real user
        slowMo: 200, // Even more delays between actions
        defaultViewport: null, // Use default viewport
        args: [
          "--start-maximized",
          "--no-sandbox",
          "--disable-setuid-sandbox",
          "--disable-dev-shm-usage",
          "--disable-web-security",
          "--disable-features=VizDisplayCompositor",
          "--disable-blink-features=AutomationControlled",
          "--disable-plugins-discovery",
          "--no-first-run",
          "--no-default-browser-check",
          "--disable-default-apps",
          "--disable-popup-blocking",
          "--disable-translate",
          "--disable-background-timer-throttling",
          "--disable-renderer-backgrounding",
          "--disable-backgrounding-occluded-windows",
          "--disable-ipc-flooding-protection",
          "--ignore-certificate-errors",
          "--ignore-ssl-errors",
          "--ignore-certificate-errors-spki-list",
        ],
      });

      // Set additional properties to look more human
      const pages = await this.browser.pages();
      const page = pages[0];

      // Set the random user agent
      await page.setUserAgent(userAgent);

      // Remove automation indicators
      await page.evaluateOnNewDocument(() => {
        Object.defineProperty(navigator, "webdriver", {
          get: () => undefined,
        });

        // Mock plugins
        Object.defineProperty(navigator, "plugins", {
          get: () => [
            { name: "Chrome PDF Plugin" },
            { name: "Chrome PDF Viewer" },
            { name: "Native Client" },
          ],
        });

        // Mock languages
        Object.defineProperty(navigator, "languages", {
          get: () => ["en-US", "en", "vi"],
        });

        // Mock permissions
        const originalQuery = window.navigator.permissions.query;
        window.navigator.permissions.query = (parameters) =>
          parameters.name === "notifications"
            ? Promise.resolve({ state: Notification.permission })
            : originalQuery(parameters);
      });
    }
    return this.browser;
  }

  async closeBrowser() {
    if (this.browser) {
      await this.browser.close();
      this.browser = null;
    }
  }

  async simulateHumanMouseMovement(page) {
    console.log("🖱️ Simulating realistic human mouse movements...");

    try {
      // Get page dimensions
      const dimensions = await page.evaluate(() => {
        return {
          width: window.innerWidth,
          height: window.innerHeight,
        };
      });

      // Simulate natural mouse movements like a real person browsing
      const movements = [
        // Start near top-left (like someone just opened the page)
        { x: 100, y: 150 },
        // Move to center area (reading content)
        { x: dimensions.width * 0.4, y: dimensions.height * 0.3 },
        // Move around like reading
        { x: dimensions.width * 0.6, y: dimensions.height * 0.4 },
        // Scroll area
        { x: dimensions.width * 0.5, y: dimensions.height * 0.6 },
        // Back to top area
        { x: dimensions.width * 0.3, y: dimensions.height * 0.2 },
      ];

      for (let i = 0; i < movements.length; i++) {
        const move = movements[i];

        // Add some randomness to make it more natural
        const randomX = move.x + (Math.random() - 0.5) * 100;
        const randomY = move.y + (Math.random() - 0.5) * 100;

        await page.mouse.move(randomX, randomY, { steps: 10 });

        // Random pause like a human thinking/reading
        const pauseTime = Math.random() * 2000 + 500; // 0.5-2.5 seconds
        await new Promise((resolve) => setTimeout(resolve, pauseTime));

        // Sometimes click (like selecting text or clicking something)
        if (Math.random() < 0.3) {
          // 30% chance
          await page.mouse.click(randomX, randomY);
          await new Promise((resolve) => setTimeout(resolve, 300));
        }
      }

      console.log("✅ Human mouse simulation completed");
    } catch (e) {
      console.log("⚠️ Could not simulate mouse movements:", e.message);
    }
  }

  async fetchPageWithBrowser(url) {
    const browser = await this.initBrowser();
    const page = await browser.newPage();

    try {
      // Apply proxy to this page if available
      if (this.currentProxy) {
        console.log(`🌍 Applying proxy: ${this.currentProxy}`);
        try {
          await useProxy(page, this.currentProxy);
        } catch (e) {
          console.log(
            `⚠️ Proxy failed, continuing with direct connection: ${e.message}`
          );
        }
      } else {
        console.log("🌍 Using direct connection (no proxy)");
      }

      // Set realistic headers from different countries
      const headers = {
        "Accept-Language": "en-US,en;q=0.9,vi;q=0.8,fr;q=0.7",
        Accept:
          "text/html,application/xhtml+xml,application/xml;q=0.9,image/avif,image/webp,*/*;q=0.8",
        "Cache-Control": "max-age=0",
        "Sec-Fetch-Dest": "document",
        "Sec-Fetch-Mode": "navigate",
        "Sec-Fetch-Site": "none",
        "Sec-Fetch-User": "?1",
      };

      await page.setExtraHTTPHeaders(headers);

      console.log(`👤 Human from random country navigating to: ${url}`);

      // Navigate like a human - load page and wait 10 seconds
      console.log("🔄 Loading page...");
      await page.goto(url, {
        waitUntil: "load", // Wait until page load event fires
        timeout: 120000, // Give it 2 minutes like a patient human
      });

      // Wait 10 seconds for dynamic content to load instead of waiting for network idle
      console.log("⏳ Waiting 10 seconds for dynamic content to load...");
      await new Promise((resolve) => setTimeout(resolve, 10000));

      console.log("👀 Page loaded, behaving like a human user...");

      // Human-like pause after page load (like reading the page)
      const readingTime = Math.floor(Math.random() * 3000) + 2000; // 2-5 seconds
      console.log(
        `📖 Taking ${Math.floor(
          readingTime / 1000
        )} seconds to "read" the page...`
      );
      await new Promise((resolve) => setTimeout(resolve, readingTime));

      // Scroll from top to bottom to load dynamic content (for all pages)
      console.log("📜 Scrolling through page to load dynamic content...");
      try {
        // Get page height
        const pageHeight = await page.evaluate(() => {
          return Math.max(
            document.body.scrollHeight,
            document.body.offsetHeight,
            document.documentElement.clientHeight,
            document.documentElement.scrollHeight,
            document.documentElement.offsetHeight
          );
        });

        console.log(`📏 Page height: ${pageHeight}px`);

        // Scroll down in steps like a human reading
        const scrollSteps = 5; // Number of scroll steps
        const stepHeight = Math.floor(pageHeight / scrollSteps);

        for (let i = 1; i <= scrollSteps; i++) {
          const scrollTo = Math.min(stepHeight * i, pageHeight);

          await page.evaluate((scrollPosition) => {
            window.scrollTo({ top: scrollPosition, behavior: "smooth" });
          }, scrollTo);

          // Random wait time between 1-4 seconds to simulate human reading
          const waitTime = Math.floor(Math.random() * 3000) + 1000; // 1-4 seconds
          console.log(
            `📖 Scrolled to ${scrollTo}px, waiting ${Math.floor(
              waitTime / 1000
            )}s...`
          );
          await new Promise((resolve) => setTimeout(resolve, waitTime));
        }

        // Final wait at bottom
        console.log(
          "⏳ Reached bottom, waiting 3 seconds for content to load..."
        );
        await new Promise((resolve) => setTimeout(resolve, 3000));

        // Scroll back to top
        await page.evaluate(() => {
          window.scrollTo({ top: 0, behavior: "smooth" });
        });
        await new Promise((resolve) => setTimeout(resolve, 1000));

        console.log("✅ Page scrolling completed");
      } catch (e) {
        console.log("⚠️ Could not scroll page, continuing...", e.message);
      }

      // Check if we're on a Cloudflare challenge page
      const isChallengePage = await page.evaluate(() => {
        return (
          document.title.includes("Just a moment") ||
          document.body.textContent.includes("Checking your browser") ||
          document.body.textContent.includes("challenges.cloudflare.com") ||
          document.body.textContent.includes("Please wait")
        );
      });

      if (isChallengePage) {
        console.log("🔒 Detected challenge page, waiting 30 seconds...");

        try {
          await page.waitForFunction(
            () => {
              const title = document.title;
              const bodyText = document.body.textContent;

              // Check if we're no longer on challenge page
              const notChallenge =
                !title.includes("Just a moment") &&
                !bodyText.includes("Checking your browser") &&
                !bodyText.includes("challenges.cloudflare.com") &&
                !bodyText.includes("Please wait") &&
                !bodyText.includes("Verifying you are human");

              // Also check for positive signs of real content
              const hasRealContent =
                bodyText.length > 5000 ||
                document.querySelectorAll("img").length > 3 ||
                document.querySelectorAll("a[href]").length > 10;

              return notChallenge && hasRealContent;
            },
            { timeout: 30000 }
          ); // 30 seconds only

          console.log("✅ Challenge completed successfully!");
        } catch (e) {
          console.log(
            "⏰ Challenge timeout after 30 seconds, proceeding anyway..."
          );
        }

        // Brief delay after challenge
        await new Promise((resolve) => setTimeout(resolve, 2000));

        // Only simulate mouse movements for challenge pages (no scrolling since we already scrolled)
        console.log("👤 Acting like a real person after challenge...");
        await this.simulateHumanMouseMovement(page);
      } else {
        console.log(
          "✅ No challenge detected, skipping mouse simulation to avoid interference"
        );
      }

      // Wait for specific content that indicates successful page load
      console.log("🔍 Waiting for real content to appear...");

      // Try to wait for Chotot-specific elements that indicate real content
      const contentSelectors = [
        '[class*="AdItem"]',
        '[class*="listing"]',
        '[data-testid*="listing"]',
        ".item-title",
        ".ad-item",
        'img[alt*="apartment"]',
        'img[alt*="house"]',
        'a[href*="/mua-ban"]',
        'a[href*="/thue"]',
      ];

      let foundContent = false;
      for (const selector of contentSelectors) {
        try {
          await page.waitForSelector(selector, { timeout: 10000 });
          console.log(`✅ Found content with selector: ${selector}`);
          foundContent = true;
          break;
        } catch (e) {
          // Continue to next selector
        }
      }

      if (!foundContent) {
        console.log(
          "⚠️ No specific content selectors found, checking for general content..."
        );

        // Check if we have moved past the challenge page
        const hasRealContent = await page.evaluate(() => {
          // Look for signs of real content
          const hasImages = document.querySelectorAll("img").length > 2;
          const hasLinks = document.querySelectorAll("a[href]").length > 5;
          const hasText = document.body.textContent.length > 1000;
          const notChallengePage =
            !document.body.textContent.includes("Just a moment") &&
            !document.body.textContent.includes("Checking your browser");

          return hasImages && hasLinks && hasText && notChallengePage;
        });

        if (hasRealContent) {
          console.log("✅ Detected real page content loaded");
        } else {
          console.log("⚠️ Still appears to be on challenge/loading page");
        }
      }

      // Final wait for dynamic content
      await new Promise((resolve) => setTimeout(resolve, 2000));

      // Get the page content
      const content = await page.content();

      // Check if we still have a challenge page
      if (
        content.includes("Just a moment") ||
        content.includes("challenges.cloudflare.com")
      ) {
        console.log("⚠️ Still on Cloudflare challenge page");
      }

      // Save HTML for debugging (optional)
      if (process.env.DEBUG_HTML === "true") {
        const fs = require("fs").promises;
        await fs.writeFile("./debug-page.html", content);
        console.log("💾 Saved page HTML to debug-page.html");
      }

      console.log(
        `✅ Page loaded successfully, content length: ${content.length}`
      );
      return content;
    } catch (error) {
      console.error(`❌ Browser fetch failed: ${error.message}`);
      throw error;
    } finally {
      await page.close();
    }
  }

  extractItems(html, url, siteName) {
    const cheerio = require("cheerio");
    const $ = cheerio.load(html);
    const items = [];

    // Get domain for source identification
    const domain = new URL(url).hostname;

    console.log(`🔍 Extracting items from ${siteName} (${domain})`);

    // Specific selectors for Batdongsan.com.vn
    if (domain.includes("batdongsan.com.vn")) {
      console.log("🏠 Using Batdongsan.com.vn specific selectors");

      // Main property listing cards
      const propertyCards = $(
        ".js__card.js__card-full-web.pr-container.re__card-full"
      );
      console.log(`   Found ${propertyCards.length} main property cards`);

      propertyCards.each((_, element) => {
        const $element = $(element);

        // Extract title from the main link
        const titleEl = $element.find(".pr-title.js__card-title");
        const title = titleEl.text().trim();

        // Extract URL from the main link
        const linkEl = $element
          .find("a.js__product-link-for-product-id")
          .first();
        let itemUrl = linkEl.attr("href") || "";
        if (itemUrl && !itemUrl.startsWith("http")) {
          itemUrl = `https://${domain}${itemUrl}`;
        }

        // Extract price
        const priceEl = $element.find(
          ".re__card-config-price.js__card-config-item"
        );
        const price = priceEl.text().trim() || "Giá liên hệ";

        // Extract image
        const imgEl = $element.find(".re__card-image img").first();
        let imageUrl = imgEl.attr("src") || imgEl.attr("data-src") || "";
        if (imageUrl && !imageUrl.startsWith("http")) {
          imageUrl = `https:${imageUrl}`;
        }

        // Extract area and other details
        const areaEl = $element.find(
          ".re__card-config-area.js__card-config-item"
        );
        const area = areaEl.text().trim();

        const bedroomEl = $element.find(
          ".re__card-config-bedroom.js__card-config-item span"
        );
        const bedroom = bedroomEl.text().trim();

        const locationEl = $element.find(".re__card-location span");
        const location = locationEl.text().trim();

        // Extract posting time
        const timeEl = $element.find(".re__card-published-info-published-at");
        let postedTime = timeEl.first().text().trim();
        // Clean up the posting time text (remove extra whitespace and duplicates)
        if (postedTime) {
          postedTime = postedTime.split("\n")[0].trim();
        }

        if (title && title.length > 10) {
          items.push({
            title: title.trim(),
            url: itemUrl,
            price: price,
            image: imageUrl,
            area: area,
            bedroom: bedroom ? `${bedroom} phòng ngủ` : "",
            location: location,
            postedTime: postedTime,
            source: siteName,
            domain: domain,
            extractedAt: new Date().toISOString(),
          });
        }
      });

      // Also check for compact cards (verified listings)
      const compactCards = $(
        ".js__card.js__card-compact-web.pr-container.re__card-compact"
      );
      console.log(`   Found ${compactCards.length} compact property cards`);

      compactCards.each((_, element) => {
        const $element = $(element);

        const titleEl = $element.find(".js__card-title");
        const title = titleEl.text().trim();

        const linkEl = $element
          .find("a.js__product-link-for-product-id")
          .first();
        let itemUrl = linkEl.attr("href") || "";
        if (itemUrl && !itemUrl.startsWith("http")) {
          itemUrl = `https://${domain}${itemUrl}`;
        }

        const priceEl = $element.find(".re__card-config-price");
        const price = priceEl.text().trim() || "Giá liên hệ";

        const imgEl = $element.find(".pr-img").first();
        let imageUrl = imgEl.attr("src") || imgEl.attr("data-src") || "";
        if (imageUrl && !imageUrl.startsWith("http")) {
          imageUrl = `https:${imageUrl}`;
        }

        const areaEl = $element.find(".re__card-config-area");
        const area = areaEl.text().replace("·", "").trim();

        const locationEl = $element.find(".re__card-location span");
        const location = locationEl.text().trim();

        // Extract posting time
        const timeEl = $element.find(".re__card-published-info-published-at");
        let postedTime = timeEl.first().text().trim();
        // Clean up the posting time text (remove extra whitespace and duplicates)
        if (postedTime) {
          postedTime = postedTime.split("\n")[0].trim();
        }

        if (title && title.length > 10) {
          items.push({
            title: title.trim(),
            url: itemUrl,
            price: price,
            image: imageUrl,
            area: area,
            bedroom: "",
            location: location,
            postedTime: postedTime,
            source: siteName,
            domain: domain,
            extractedAt: new Date().toISOString(),
          });
        }
      });
    } else {
      // Fallback to generic selectors for other sites
      console.log("🔍 Using generic selectors for other sites");

      const selectors = [
        '[data-testid*="listing"]',
        '[class*="listing"]',
        '[class*="ad-item"]',
        '[class*="item-card"]',
        '[class*="property"]',
        "article",
        ".item",
        ".listing",
        ".post",
        ".product",
        ".card",
        '[class*="item"]',
        '[class*="card"]',
      ];

      for (const selector of selectors) {
        const elements = $(selector);
        console.log(
          `   Selector "${selector}": found ${elements.length} elements`
        );

        if (elements.length > 0) {
          elements.each((_, element) => {
            const $element = $(element);

            // Generic title extraction
            let title = "";
            const titleSelectors = [
              "h1",
              "h2",
              "h3",
              "h4",
              ".title",
              '[class*="title"]',
              "a[title]",
              "a",
            ];
            for (const titleSelector of titleSelectors) {
              const titleEl = $element.find(titleSelector).first();
              if (titleEl.length > 0) {
                title = titleEl.attr("title") || titleEl.text().trim();
                if (title && title.length > 5) break;
              }
            }

            // Generic URL extraction
            let itemUrl = "";
            const linkEl = $element.find("a").first();
            if (linkEl.length > 0) {
              itemUrl = linkEl.attr("href");
              if (itemUrl && !itemUrl.startsWith("http")) {
                try {
                  const baseUrl = new URL(url);
                  itemUrl = new URL(itemUrl, baseUrl.origin).href;
                } catch (e) {
                  itemUrl = url;
                }
              }
            }

            // Generic price extraction
            let price = "";
            const priceSelectors = [
              '[class*="price"]',
              'span:contains("₫")',
              'span:contains("triệu")',
              'span:contains("tỷ")',
            ];
            for (const priceSelector of priceSelectors) {
              const priceEl = $element.find(priceSelector).first();
              if (priceEl.length > 0) {
                const priceText = priceEl.text().trim();
                if (
                  priceText &&
                  (priceText.includes("₫") ||
                    priceText.includes("triệu") ||
                    priceText.includes("tỷ"))
                ) {
                  price = priceText;
                  break;
                }
              }
            }

            // Generic image extraction
            let imageUrl = "";
            const imgEl = $element.find("img[src]").first();
            if (imgEl.length > 0) {
              let imgSrc = imgEl.attr("src") || imgEl.attr("data-src");
              if (imgSrc && !imgSrc.startsWith("http")) {
                try {
                  const baseUrl = new URL(url);
                  imageUrl = new URL(imgSrc, baseUrl.origin).href;
                } catch (e) {
                  imageUrl = imgSrc;
                }
              } else {
                imageUrl = imgSrc || "";
              }
            }

            // Generic posting time extraction
            let postedTime = "";
            const timeSelectors = [
              ".re__card-published-info-published-at",
              '[class*="time"]',
              '[class*="date"]',
              '[class*="posted"]',
            ];
            for (const timeSelector of timeSelectors) {
              const timeEl = $element.find(timeSelector).first();
              if (timeEl.length > 0) {
                let timeText = timeEl.text().trim();
                if (
                  timeText &&
                  (timeText.includes("Đăng") ||
                    timeText.includes("ngày") ||
                    timeText.includes("giờ"))
                ) {
                  // Clean up the posting time text
                  postedTime = timeText.split("\n")[0].trim();
                  break;
                }
              }
            }

            if (title && title.length > 10 && title.length < 200) {
              items.push({
                title: title.trim(),
                url: itemUrl || url,
                price: price.trim() || "Giá liên hệ",
                image: imageUrl,
                area: "",
                bedroom: "",
                location: "",
                postedTime: postedTime,
                source: siteName,
                domain: domain,
                extractedAt: new Date().toISOString(),
              });
            }
          });

          if (items.length > 0) {
            console.log(
              `✅ Found ${items.length} items with selector: ${selector}`
            );
            break;
          }
        }
      }
    }

    // Remove duplicates based on title
    const uniqueItems = items
      .filter(
        (item, index, self) =>
          index === self.findIndex((i) => i.title === item.title)
      )
      .slice(0, 50);

    console.log(
      `📄 Extracted ${uniqueItems.length} unique items from ${siteName}`
    );
    return uniqueItems;
  }

  async checkSite(site) {
    console.log(`🔍 [${site.name}] Starting browser check: ${site.url}`);

    try {
      const html = await this.fetchPageWithBrowser(site.url);
      const currentItems = this.extractItems(html, site.url, site.name);

      console.log(`📄 [${site.name}] Found ${currentItems.length} total items`);

      if (currentItems.length === 0) {
        console.log(
          `⚠️ [${site.name}] No items found - site structure might have changed or content not loading`
        );
        await this.slackNotifier.sendErrorNotification(
          site.name,
          new Error(
            "No items found - website structure may have changed or content is not loading properly"
          )
        );
        return { success: false, error: "No items found" };
      }

      // Check if this is the first run for this site
      const isFirstRun = await this.storage.isFirstRun(site.name);

      if (isFirstRun) {
        console.log(
          `🎉 [${site.name}] First run - sending all ${currentItems.length} items`
        );

        // Mark all items as seen for future runs
        for (const item of currentItems) {
          await this.storage.addSeenItem(site.name, item);
        }

        // Mark that we've completed the first run
        await this.storage.markFirstRunComplete(site.name);

        // Send all items as "initial items"
        await this.slackNotifier.sendInitialItemsNotification(
          site.name,
          currentItems
        );

        return {
          success: true,
          newItems: currentItems.length,
          totalItems: currentItems.length,
          isFirstRun: true,
        };
      } else {
        // Regular run - check for new items only
        const newItems = [];
        for (const item of currentItems) {
          if (await this.storage.isNewItem(site.name, item)) {
            newItems.push(item);
            await this.storage.addSeenItem(site.name, item);
          }
        }

        if (newItems.length > 0) {
          console.log(`🆕 [${site.name}] Found ${newItems.length} new items`);
          await this.slackNotifier.sendNewItemNotification(site.name, newItems);
          return {
            success: true,
            newItems: newItems.length,
            totalItems: currentItems.length,
          };
        } else {
          console.log(
            `✅ [${site.name}] No new items (${currentItems.length} total items checked)`
          );
          await this.slackNotifier.sendSuccessfulCheckNotification(
            site.name,
            currentItems.length
          );
          return {
            success: true,
            newItems: 0,
            totalItems: currentItems.length,
          };
        }
      }
    } catch (error) {
      console.error(`❌ [${site.name}] Browser check error:`, error.message);
      await this.slackNotifier.sendErrorNotification(site.name, error);
      return { success: false, error: error.message };
    }
  }

  async testProxy(proxy) {
    console.log(`🔍 Testing proxy: ${proxy}`);
    const browser = await this.initBrowser();
    const page = await browser.newPage();

    try {
      // Apply proxy to this page
      await useProxy(page, proxy);

      // Test with a simple IP checking service
      const testUrl = 'https://httpbin.org/ip';
      console.log(`📡 Testing proxy with ${testUrl}`);

      await page.goto(testUrl, {
        waitUntil: 'networkidle0',
        timeout: 30000
      });

      // Get the IP response
      const content = await page.content();
      const ipMatch = content.match(/"origin":\s*"([^"]+)"/);

      if (ipMatch) {
        const detectedIP = ipMatch[1];
        console.log(`✅ Proxy ${proxy} working - IP: ${detectedIP}`);
        return { working: true, ip: detectedIP, proxy: proxy };
      } else {
        console.log(`❌ Proxy ${proxy} failed - Could not detect IP`);
        return { working: false, error: 'Could not detect IP', proxy: proxy };
      }
    } catch (error) {
      console.log(`❌ Proxy ${proxy} failed - ${error.message}`);
      return { working: false, error: error.message, proxy: proxy };
    } finally {
      await page.close();
    }
  }

  async testAllProxies() {
    console.log(`🧪 Testing all ${this.freeProxies.length} proxies...`);

    const results = {
      total: this.freeProxies.length,
      working: [],
      failed: [],
    };

    try {
      await this.initBrowser();

      for (let i = 0; i < this.freeProxies.length; i++) {
        const proxy = this.freeProxies[i];
        console.log(`\n🔍 [${i + 1}/${this.freeProxies.length}] Testing proxy: ${proxy}`);

        const result = await this.testProxy(proxy);

        if (result.working) {
          results.working.push(result);
          console.log(`✅ Proxy ${proxy} is working`);
        } else {
          results.failed.push(result);
          console.log(`❌ Proxy ${proxy} failed: ${result.error}`);
        }

        // Small delay between tests
        if (i < this.freeProxies.length - 1) {
          await new Promise(resolve => setTimeout(resolve, 2000));
        }
      }
    } finally {
      await this.closeBrowser();
    }

    console.log(`\n📊 Proxy Test Results:`);
    console.log(`   ✅ Working: ${results.working.length}/${results.total}`);
    console.log(`   ❌ Failed: ${results.failed.length}/${results.total}`);

    if (results.working.length > 0) {
      console.log(`\n🌍 Working proxies:`);
      results.working.forEach((result, index) => {
        console.log(`   ${index + 1}. ${result.proxy} (IP: ${result.ip})`);
      });
    }

    if (results.failed.length > 0) {
      console.log(`\n❌ Failed proxies:`);
      results.failed.forEach((result, index) => {
        console.log(`   ${index + 1}. ${result.proxy} - ${result.error}`);
      });
    }

    return results;
  }

  async checkAllSites(sites) {
    console.log(
      `🚀 Starting browser-based check of ${sites.length} Vietnamese real estate sites...`
    );

    const results = {
      total: sites.length,
      successful: 0,
      failed: 0,
      newItemsFound: 0,
      failedSites: [],
    };

    try {
      await this.initBrowser();

      for (let i = 0; i < sites.length; i++) {
        const site = sites[i];
        console.log(
          `\n🌐 [${i + 1}/${sites.length}] Browser processing ${site.name}...`
        );

        const result = await this.checkSite(site);

        if (result.success) {
          results.successful++;
          results.newItemsFound += result.newItems || 0;
          console.log(`✅ [${site.name}] Browser check completed successfully`);
        } else {
          results.failed++;
          results.failedSites.push({
            name: site.name,
            error: result.error,
          });
          console.log(
            `❌ [${site.name}] Browser check failed: ${result.error}`
          );
        }

        // Add delay between requests to be respectful
        if (i < sites.length - 1) {
          const delay = 5000; // 5 seconds between sites for faster monitoring
          console.log(`⏳ Waiting ${delay / 1000} seconds before next site...`);
          await new Promise((resolve) => setTimeout(resolve, delay));
        }
      }
    } finally {
      await this.closeBrowser();
    }

    // Send summary notification
    await this.sendSummaryNotification(results);

    console.log("\n📊 Browser Check Summary:");
    console.log(`   ✅ Successful: ${results.successful}/${results.total}`);
    console.log(`   ❌ Failed: ${results.failed}/${results.total}`);
    console.log(`   🆕 New items found: ${results.newItemsFound}`);
    if (results.failedSites.length > 0) {
      console.log(
        `   🚫 Failed sites: ${results.failedSites
          .map((s) => s.name)
          .join(", ")}`
      );
    }
    console.log("✅ Finished checking all sites");

    return results;
  }

  async sendSummaryNotification(results) {
    try {
      if (results.failed > 0 || results.newItemsFound > 0) {
        const failedSitesList =
          results.failedSites.length > 0
            ? results.failedSites
              .map((site) => `• ${site.name}: ${site.error}`)
              .join("\n")
            : "None";

        const message = {
          text: `🌐 Browser Monitoring Summary - ${results.successful}/${results.total} sites successful`,
          blocks: [
            {
              type: "header",
              text: {
                type: "plain_text",
                text: "🌐 Browser-Based Monitoring Summary",
              },
            },
            {
              type: "section",
              text: {
                type: "mrkdwn",
                text: `*Mode:* Browser-based (Anti-bot protection bypass)\n*Total Sites:* ${results.total}\n*Successful:* ${results.successful}\n*Failed:* ${results.failed}\n*New Items:* ${results.newItemsFound}`,
              },
            },
          ],
        };

        if (results.failedSites.length > 0) {
          message.blocks.push({
            type: "section",
            text: {
              type: "mrkdwn",
              text: `*Failed Sites:*\n${failedSitesList}`,
            },
          });
        }

        message.blocks.push({
          type: "context",
          elements: [
            {
              type: "mrkdwn",
              text: `📅 Summary at ${new Date().toLocaleString("vi-VN", {
                timeZone: "Asia/Ho_Chi_Minh",
              })} (Vietnam time)`,
            },
          ],
        });

        await this.slackNotifier.webhook.send(message);
        console.log("📊 Browser summary notification sent to Slack");
      }
    } catch (error) {
      console.error("❌ Error sending browser summary notification:", error);
    }
  }
}

module.exports = BrowserMonitor;

// Main execution
async function main() {
  console.log("🚀 Starting Website Monitor with Browser Support...");

  // Load environment variables
  require("dotenv").config();

  console.log("📋 Environment variables loaded:");
  console.log(
    `   SLACK_WEBHOOK_URL: ${process.env.SLACK_WEBHOOK_URL ? "Set" : "Not set"}`
  );
  console.log(
    `   CHECK_INTERVAL_MINUTES: ${process.env.CHECK_INTERVAL_MINUTES || "Default (240)"
    }`
  );
  console.log(
    `   USE_BROWSER: ${process.env.USE_BROWSER || "Default (false)"}`
  );
  console.log(`   DEBUG_HTML: ${process.env.DEBUG_HTML || "Default (false)"}`);

  // Validate required environment variables
  if (!process.env.SLACK_WEBHOOK_URL) {
    console.error("❌ SLACK_WEBHOOK_URL environment variable is required");
    process.exit(1);
  }

  console.log("🔧 Creating monitor instance...");
  const monitor = new BrowserMonitor(process.env.SLACK_WEBHOOK_URL);

  // Load sites from urls.json
  console.log("📄 Loading sites from urls.json...");
  let sites;
  try {
    const fs = require("fs");
    const urlsData = JSON.parse(fs.readFileSync("./urls.json", "utf8"));
    sites = urlsData.site;
    console.log(`✅ Loaded ${sites.length} site(s) from urls.json`);
    sites.forEach((site, index) => {
      console.log(`   ${index + 1}. ${site.name}: ${site.url}`);
    });
  } catch (error) {
    console.error("❌ Error loading urls.json:", error.message);
    console.log("📄 Using fallback configuration...");
    sites = [
      {
        name: "Batdongsan.com.vn",
        url: "https://batdongsan.com.vn/cho-thue-can-ho-chung-cu-quan-7?gtn=5-trieu&gcn=8-trieu",
      },
    ];
  }

  console.log("▶️ Starting monitor...");

  // Send startup notification
  await monitor.slackNotifier.sendStartupNotification(sites);

  // Run initial check
  console.log("🔍 Running initial check...");
  await monitor.checkAllSites(sites);

  // Set up interval for regular checks
  const intervalMinutes = parseInt(process.env.CHECK_INTERVAL_MINUTES) || 240;
  console.log(
    `⏰ Setting up monitoring interval: every ${intervalMinutes} minutes`
  );

  setInterval(async () => {
    console.log(
      `\n🔄 Starting scheduled check at ${new Date().toLocaleString("vi-VN", {
        timeZone: "Asia/Ho_Chi_Minh",
      })}`
    );
    await monitor.checkAllSites(sites);
  }, intervalMinutes * 60 * 1000);

  console.log("✅ Monitor is running! Press Ctrl+C to stop.");
}

// Handle graceful shutdown
process.on("SIGINT", () => {
  console.log("\n🛑 Received SIGINT, shutting down gracefully...");
  process.exit(0);
});

process.on("SIGTERM", () => {
  console.log("\n🛑 Received SIGTERM, shutting down gracefully...");
  process.exit(0);
});

// Start the application
if (require.main === module) {
  console.log("🎯 Script started as main module");
  main().catch((error) => {
    console.error("❌ Fatal error:", error);
    process.exit(1);
  });
} else {
  console.log("📦 Script loaded as module");
}
