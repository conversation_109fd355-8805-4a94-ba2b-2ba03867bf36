#!/usr/bin/env node

const Storage = require('./src/storage');

async function clearStorage() {
    console.log('🚀 Storage Cleaner Tool');
    console.log('========================');
    
    const storage = new Storage();
    
    try {
        // Get command line arguments
        const args = process.argv.slice(2);
        
        if (args.length === 0) {
            // Clear all data
            console.log('🗑️ Clearing ALL stored data...');
            console.log('⚠️ This will delete all seen items and first-run markers for all sites');
            
            const deletedCount = await storage.clearAllData();
            
            if (deletedCount > 0) {
                console.log('\n✅ Storage cleared successfully!');
                console.log('📝 Next monitoring run will treat all sites as first run');
                console.log('📧 All items will be sent as "initial items" notifications');
            } else {
                console.log('\n📁 No storage files found to clear');
            }
            
        } else if (args[0] === '--site' && args[1]) {
            // Clear specific site data
            const siteName = args[1];
            console.log(`🗑️ Clearing data for specific site: ${siteName}`);
            
            await storage.clearSiteData(siteName);
            
            console.log('\n✅ Site data cleared successfully!');
            console.log(`📝 Next monitoring run will treat "${siteName}" as first run`);
            
        } else if (args[0] === '--help' || args[0] === '-h') {
            // Show help
            console.log('📖 Usage:');
            console.log('  node clear-storage.js                    # Clear all storage data');
            console.log('  node clear-storage.js --site "Site Name" # Clear specific site data');
            console.log('  node clear-storage.js --help             # Show this help');
            console.log('');
            console.log('📁 Storage files are located in the ./data directory');
            console.log('🔄 After clearing, sites will be treated as first run');
            
        } else {
            console.log('❌ Invalid arguments. Use --help for usage information');
            process.exit(1);
        }
        
    } catch (error) {
        console.error('❌ Error clearing storage:', error.message);
        process.exit(1);
    }
}

// Handle graceful shutdown
process.on('SIGINT', () => {
    console.log('\n🛑 Operation cancelled by user');
    process.exit(0);
});

// Run the script
if (require.main === module) {
    clearStorage().catch(error => {
        console.error('❌ Fatal error:', error);
        process.exit(1);
    });
}

module.exports = clearStorage;
