# Slack Webhook URL for notifications
SLACK_WEBHOOK_URL=https://hooks.slack.com/services/YOUR/SLACK/WEBHOOK

# Check interval in minutes (default: 30)
CHECK_INTERVAL_MINUTES=30

# Use browser-based monitoring (true/false) - now runs in headless mode with Cloudflare bypass
USE_BROWSER=false

# Run browser in headless mode (true/false) - headless is invisible and faster
HEADLESS_BROWSER=true

# Log level (debug, info, warn, error)
LOG_LEVEL=info
