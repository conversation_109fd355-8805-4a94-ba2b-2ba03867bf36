#!/usr/bin/env node

/**
 * Test script for headless browser functionality
 * This script tests the new headless browser configuration
 */

require('dotenv').config();
const BrowserMonitor = require('./src/browser-monitor');

async function testHeadlessBrowser() {
    console.log('🧪 Testing Headless Browser Configuration...');
    console.log('=' .repeat(60));
    
    // Force headless mode for this test
    process.env.HEADLESS_BROWSER = 'true';
    
    // Create a browser monitor instance
    const monitor = new BrowserMonitor('dummy-webhook-url');
    
    try {
        console.log('🌐 Testing headless browser initialization...');
        
        // Test with a simple website first
        const testUrl = 'https://httpbin.org/user-agent';
        console.log(`📡 Testing with ${testUrl}`);
        
        const content = await monitor.fetchPageWithBrowser(testUrl);
        
        if (content && content.length > 0) {
            console.log('✅ Headless browser test successful!');
            console.log(`📄 Content length: ${content.length} characters`);
            
            // Check if user agent was set correctly
            if (content.includes('Chrome') || content.includes('Mozilla')) {
                console.log('✅ User agent successfully set');
            } else {
                console.log('⚠️ User agent might not be set correctly');
            }
        } else {
            console.log('❌ No content received from headless browser');
        }
        
        // Test with a Cloudflare-protected site (optional)
        console.log('\n🔒 Testing Cloudflare bypass (optional)...');
        try {
            const cfTestUrl = 'https://www.batdongsan.com.vn/nha-dat-ban';
            console.log(`🏠 Testing with ${cfTestUrl}`);
            
            const cfContent = await monitor.fetchPageWithBrowser(cfTestUrl);
            
            if (cfContent && cfContent.length > 10000) {
                console.log('✅ Cloudflare bypass test successful!');
                console.log(`📄 Content length: ${cfContent.length} characters`);
                
                // Check for challenge indicators
                if (cfContent.includes('Just a moment') || cfContent.includes('challenges.cloudflare.com')) {
                    console.log('⚠️ Still showing Cloudflare challenge page');
                } else {
                    console.log('✅ Successfully bypassed Cloudflare protection');
                }
            } else {
                console.log('⚠️ Cloudflare test returned limited content');
            }
        } catch (cfError) {
            console.log(`⚠️ Cloudflare test failed: ${cfError.message}`);
        }
        
    } catch (error) {
        console.error('❌ Headless browser test failed:', error.message);
        console.error(error.stack);
    } finally {
        // Clean up
        await monitor.closeBrowser();
        console.log('\n🧹 Browser closed');
    }
    
    console.log('\n' + '='.repeat(60));
    console.log('🏁 Headless Browser Test Complete');
}

// Test visible browser mode
async function testVisibleBrowser() {
    console.log('\n🧪 Testing Visible Browser Configuration...');
    console.log('=' .repeat(60));
    
    // Force visible mode for this test
    process.env.HEADLESS_BROWSER = 'false';
    
    // Create a browser monitor instance
    const monitor = new BrowserMonitor('dummy-webhook-url');
    
    try {
        console.log('🌐 Testing visible browser initialization...');
        console.log('⚠️ This will open a browser window for 10 seconds...');
        
        // Test with a simple website
        const testUrl = 'https://httpbin.org/user-agent';
        console.log(`📡 Testing with ${testUrl}`);
        
        const content = await monitor.fetchPageWithBrowser(testUrl);
        
        if (content && content.length > 0) {
            console.log('✅ Visible browser test successful!');
            console.log(`📄 Content length: ${content.length} characters`);
        } else {
            console.log('❌ No content received from visible browser');
        }
        
        // Wait a bit so user can see the browser
        console.log('⏳ Waiting 5 seconds before closing...');
        await new Promise(resolve => setTimeout(resolve, 5000));
        
    } catch (error) {
        console.error('❌ Visible browser test failed:', error.message);
    } finally {
        // Clean up
        await monitor.closeBrowser();
        console.log('🧹 Browser closed');
    }
}

// Main test function
async function runTests() {
    const args = process.argv.slice(2);
    
    if (args.includes('--visible')) {
        await testVisibleBrowser();
    } else if (args.includes('--both')) {
        await testHeadlessBrowser();
        await testVisibleBrowser();
    } else {
        // Default: test headless only
        await testHeadlessBrowser();
    }
}

// Handle command line arguments
if (require.main === module) {
    console.log('🚀 Browser Configuration Test');
    console.log('Usage:');
    console.log('  node test-headless.js           # Test headless mode only');
    console.log('  node test-headless.js --visible # Test visible mode only');
    console.log('  node test-headless.js --both    # Test both modes');
    console.log('');
    
    runTests().catch(error => {
        console.error('❌ Test failed:', error);
        process.exit(1);
    });
}
