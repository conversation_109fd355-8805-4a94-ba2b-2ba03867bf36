#!/bin/bash

# Website Monitor Setup Script
# This script helps you set up the website monitoring application

echo "🚀 Website Monitor Setup Script"
echo "================================"
echo ""

# Check if Node.js is installed
if ! command -v node &> /dev/null; then
    echo "❌ Node.js is not installed!"
    echo "Please install Node.js from https://nodejs.org/"
    echo "Then run this script again."
    exit 1
fi

# Check Node.js version
NODE_VERSION=$(node --version)
echo "✅ Node.js found: $NODE_VERSION"

# Install dependencies
echo ""
echo "📦 Installing dependencies..."
npm install

if [ $? -eq 0 ]; then
    echo "✅ Dependencies installed successfully!"
else
    echo "❌ Failed to install dependencies"
    exit 1
fi

# Create .env file if it doesn't exist
if [ ! -f .env ]; then
    echo ""
    echo "⚙️ Creating environment configuration..."
    cp .env.example .env
    echo "✅ Created .env file from template"
    echo ""
    echo "🔧 IMPORTANT: You need to edit the .env file with your Slack webhook URL!"
    echo "   1. Open .env in a text editor"
    echo "   2. Replace 'YOUR/SLACK/WEBHOOK' with your actual webhook URL"
    echo "   3. Save the file"
else
    echo "✅ .env file already exists"
fi

# Check if urls.json exists
if [ ! -f urls.json ]; then
    echo ""
    echo "⚠️  No urls.json file found!"
    echo "   Please create urls.json with the websites you want to monitor"
    echo "   See README.md for examples"
else
    echo "✅ urls.json configuration file found"
fi

echo ""
echo "🎉 Setup complete!"
echo ""
echo "Next steps:"
echo "1. Set up your Slack webhook (see README.md for detailed instructions)"
echo "2. Edit .env file with your webhook URL"
echo "3. Configure websites in urls.json"
echo "4. Run: npm start"
echo ""
echo "📖 For detailed instructions, see README.md"
echo "🆘 Need help? Check the troubleshooting section in README.md"
