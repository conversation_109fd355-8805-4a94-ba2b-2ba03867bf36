@echo off
REM Website Monitor Setup Script for Windows
REM This script helps you set up the website monitoring application

echo 🚀 Website Monitor Setup Script
echo ================================
echo.

REM Check if Node.js is installed
node --version >nul 2>&1
if %errorlevel% neq 0 (
    echo ❌ Node.js is not installed!
    echo Please install Node.js from https://nodejs.org/
    echo Then run this script again.
    pause
    exit /b 1
)

REM Show Node.js version
for /f "tokens=*" %%i in ('node --version') do set NODE_VERSION=%%i
echo ✅ Node.js found: %NODE_VERSION%

REM Install dependencies
echo.
echo 📦 Installing dependencies...
call npm install

if %errorlevel% neq 0 (
    echo ❌ Failed to install dependencies
    pause
    exit /b 1
)

echo ✅ Dependencies installed successfully!

REM Create .env file if it doesn't exist
if not exist .env (
    echo.
    echo ⚙️ Creating environment configuration...
    copy .env.example .env >nul
    echo ✅ Created .env file from template
    echo.
    echo 🔧 IMPORTANT: You need to edit the .env file with your Slack webhook URL!
    echo    1. Open .env in a text editor
    echo    2. Replace 'YOUR/SLACK/WEBHOOK' with your actual webhook URL
    echo    3. Save the file
) else (
    echo ✅ .env file already exists
)

REM Check if urls.json exists
if not exist urls.json (
    echo.
    echo ⚠️  No urls.json file found!
    echo    Please create urls.json with the websites you want to monitor
    echo    See README.md for examples
) else (
    echo ✅ urls.json configuration file found
)

echo.
echo 🎉 Setup complete!
echo.
echo Next steps:
echo 1. Set up your Slack webhook (see README.md for detailed instructions)
echo 2. Edit .env file with your webhook URL
echo 3. Configure websites in urls.json
echo 4. Run: npm start
echo.
echo 📖 For detailed instructions, see README.md
echo 🆘 Need help? Check the troubleshooting section in README.md
echo.
pause
