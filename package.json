{"name": "newsletter", "version": "1.0.0", "main": "index.js", "scripts": {"start": "node index.js", "dev": "node index.js", "setup": "node -e \"console.log('🚀 Run ./setup.sh (Mac/Linux) or setup.bat (Windows) to set up the application')\"", "check-config": "node -e \"const fs=require('fs'); console.log('📋 Configuration Check:'); console.log('✅ .env exists:', fs.existsSync('.env')); console.log('✅ urls.json exists:', fs.existsSync('urls.json')); if(fs.existsSync('.env')){const env=require('fs').readFileSync('.env','utf8'); console.log('✅ Slack webhook configured:', env.includes('hooks.slack.com'));} console.log('\\n📖 See README.md for setup instructions');\"", "test": "echo \"Error: no test specified\" && exit 1"}, "keywords": ["monitoring", "slack", "notifications", "website", "scraping"], "author": "", "license": "ISC", "description": "A website monitoring application that sends Slack notifications when new content is published", "engines": {"node": ">=14.0.0"}, "dependencies": {"@slack/webhook": "^7.0.5", "axios": "^1.9.0", "cheerio": "^1.1.0", "dotenv": "^16.5.0", "https-proxy-agent": "^7.0.6", "node-cron": "^4.1.0", "puppeteer": "^24.10.0", "puppeteer-extra": "^3.3.6", "puppeteer-extra-plugin-stealth": "^2.11.2", "puppeteer-page-proxy": "^1.3.0"}}