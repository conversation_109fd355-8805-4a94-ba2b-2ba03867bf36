# 🚀 Quick Start Guide

This is a simplified guide to get you started quickly. For detailed documentation, see [README.md](README.md).

## ⚡ 5-Minute Setup

### 1. Install Node.js
- Go to [nodejs.org](https://nodejs.org/)
- Download and install the LTS version
- Restart your computer after installation

### 2. Run Setup Script

**On Mac/Linux:**
```bash
./setup.sh
```

**On Windows:**
```cmd
setup.bat
```

### 3. Get Slack Webhook URL

1. Go to [api.slack.com/apps](https://api.slack.com/apps)
2. Click "Create New App" → "From scratch"
3. Name it "Website Monitor" and select your workspace
4. Click "Incoming Webhooks" → Toggle ON
5. Click "Add New Webhook to Workspace"
6. Choose a channel and click "Allow"
7. **Copy the webhook URL**

### 4. Configure the App

1. Open `.env` file in any text editor
2. Replace `YOUR/SLACK/WEBHOOK` with your actual webhook URL
3. Save the file

### 5. Add Websites to Monitor

Edit `urls.json`:
```json
{
  "site": [
    {
      "name": "My Website",
      "url": "https://example.com/page-to-monitor"
    }
  ]
}
```

### 6. Start Monitoring

```bash
npm start
```

## 🎯 That's It!

You should now receive:
- A startup notification in Slack
- Notifications when new content is found
- Error notifications if something goes wrong

## 🔧 Common Settings

**For protected websites (like Chotot):**
```env
USE_BROWSER=true
CHECK_INTERVAL_MINUTES=240
```

**For simple websites:**
```env
USE_BROWSER=false
CHECK_INTERVAL_MINUTES=60
```

## 🆘 Need Help?

- Check [README.md](README.md) for detailed instructions
- Look at console output for error messages
- Make sure your Slack webhook URL is correct
- Verify the website URL works in your browser

## 📱 Example Slack Notification

When new content is found:
```
🔔 New items on My Website
Found 2 new items:
• New Article Title
• Another New Post
Checked at 12/15/2024, 2:30:45 PM
```

**Happy monitoring!** 🎉
